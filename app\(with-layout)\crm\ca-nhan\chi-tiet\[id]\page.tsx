'use client';
import {
    useDeleteContacts,
    useGetContactDetail,
} from '@/apis/contact/contact.api';
import { ROUTES } from '@/lib/routes';
import { useParams, useRouter } from 'next/navigation';

import { honorifics } from '@/constants/sharedData/sharedData';
import { Option } from '@/types/app.type';
import { useState } from 'react';
import { toast } from 'react-toastify';
import {
    Button,
    Card,
    CardBody,
    CardHeader,
    Col,
    DropdownItem,
    DropdownMenu,
    DropdownToggle,
    Label,
    Row,
    UncontrolledDropdown,
} from 'reactstrap';

import Evaluate from '../evaluate';
import FileInfo from '../file-info';
import FormattedDateTimeWithFormat from '@/components/common/FormattedDateTimeWithFormat';
import ModalDelete from '@/components/common/Modal/ModalDelete';
import FormatTextDetail from '@/components/common/FormatTextDetail';

const DetailPersonal = () => {
    const params = useParams();
    const id = params.id as string;
    const { data } = useGetContactDetail(id, { isUpdate: false });
    const router = useRouter();
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

    const handlePut = () => {
        router.push(ROUTES.CRM.PERSONAL.UPDATE.replace(':id', id));
    };
    const honorific = honorifics.find(
        (item: Option) => String(item.value) === String(data?.honorific),
    )?.label;

    const { mutate: deleteContacts } = useDeleteContacts({
        onSuccess: () => {
            toast.success('Xóa khách hàng thành công');
            setIsDeleteModalOpen(false);
            router.push(ROUTES.CRM.PERSONAL.INDEX);
        },
        onError: (error) => {
            toast.error(error.message || 'Xóa khách hàng thất bại');
        },
    });

    const handleConfirmDelete = () => {
        deleteContacts({ ids: [id] });
    };
    const handleClose = () => {
        setIsDeleteModalOpen(false);
    };
    return (
        <div>
            <Row>
                <Col lg={9}>
                    <Col lg={12}>
                        <Card>
                            <CardHeader style={{ borderBottom: 'none' }}>
                                <div
                                    className='d-flex align-items-center'
                                    style={{ padding: '0px 20px 0px 20px' }}
                                >
                                    <div className='flex-fill d-flex justify-content-start'>
                                        <h5 className='mb-0'>
                                            Thông tin chung
                                        </h5>
                                    </div>
                                    <div className='flex-fill d-flex justify-content-end align-items-center gap-2'>
                                        <Button
                                            className='d-flex justify-content-center align-items-center'
                                            style={{
                                                backgroundColor: 'white',
                                                color: '#0ab39c',
                                                borderColor: '#0ab39c',
                                                height: '30px',
                                                padding: '5px 8px 5px 8px',
                                            }}
                                            onClick={handlePut}
                                        >
                                            <i
                                                className=' ri-pencil-line'
                                                style={{
                                                    fontSize: '18px',
                                                    marginRight: '8px',
                                                }}
                                            ></i>
                                            Chỉnh sửa
                                        </Button>
                                        <UncontrolledDropdown>
                                            <DropdownToggle
                                                tag='button'
                                                className='btn'
                                                style={{
                                                    backgroundColor: '#0ab39c',
                                                    border: 'none',
                                                    padding: '4px',
                                                    minWidth: '30px',
                                                }}
                                            >
                                                <i
                                                    className='ri-more-fill'
                                                    style={{
                                                        color: 'white',
                                                    }}
                                                ></i>
                                            </DropdownToggle>
                                            <DropdownMenu end>
                                                <DropdownItem>
                                                    <i className='ri-user-received-line me-2'></i>
                                                    Bàn giao cá nhân
                                                </DropdownItem>
                                                <DropdownItem>
                                                    <i className='ri-history-line me-2'></i>
                                                    Nhật ký hoạt động
                                                </DropdownItem>
                                                <DropdownItem
                                                    className='text-danger'
                                                    onClick={() =>
                                                        setIsDeleteModalOpen(
                                                            true,
                                                        )
                                                    }
                                                >
                                                    <i className='ri-delete-bin-line me-2'></i>
                                                    Xóa cá nhân
                                                </DropdownItem>
                                            </DropdownMenu>
                                        </UncontrolledDropdown>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardBody>
                                <Col
                                    lg={12}
                                    style={{
                                        padding: '0px 40px 0px 40px',
                                    }}
                                >
                                    <div className='d-flex align-items-center'>
                                        <div
                                            className='flex items-center justify-center rounded-full font-bold d-flex justify-content-center align-items-center'
                                            style={{
                                                width: '60px',
                                                height: '60px',
                                                backgroundColor: '#daf4f0',
                                                color: data?.avatar,
                                                borderRadius: '50%',
                                                fontSize: '20px',
                                            }}
                                        >
                                            {data?.name
                                                ?.trim()
                                                .split(' ')
                                                .filter(Boolean)
                                                .at(1)
                                                ?.charAt(0)
                                                ?.toUpperCase() ||
                                                data?.name
                                                    ?.trim()
                                                    ?.charAt(0)
                                                    ?.toUpperCase()}
                                        </div>

                                        <Label
                                            style={{
                                                fontSize: '18px',
                                                marginLeft: '20px',
                                                marginTop: '10px',
                                            }}
                                        >
                                            {honorific} {data?.name}
                                        </Label>
                                    </div>
                                </Col>
                                <Row
                                    className='mt-4'
                                    style={{
                                        padding: '0px 40px 20px 40px',
                                    }}
                                >
                                    <Col md='4'>
                                        <Col md={12}>
                                            <FormatTextDetail
                                                label='MÃ CÁ NHÂN'
                                                p={data?.extraInfo?.code || '-'}
                                            />
                                        </Col>
                                        <Col md={12}>
                                            <FormatTextDetail
                                                label='PHÒNG BAN'
                                                p={data?.departmentName || '-'}
                                            />
                                        </Col>
                                        <Col md={12}>
                                            <Label
                                                className='text-muted'
                                                style={{
                                                    fontSize: '13px',
                                                }}
                                            >
                                                NGÀY TẠO
                                            </Label>
                                            <p style={{ fontSize: '13px' }}>
                                                <FormattedDateTimeWithFormat
                                                    date={
                                                        data?.extraInfo
                                                            ?.createdOn
                                                    }
                                                />
                                            </p>
                                        </Col>
                                    </Col>

                                    <Col md='4'>
                                        <Col md={12}>
                                            <FormatTextDetail
                                                label='EMAIL'
                                                p={data?.email || '-'}
                                            />
                                        </Col>
                                        <Col md={12}>
                                            <FormatTextDetail
                                                label='CHỨC VỤ'
                                                p={data?.positionName || '-'}
                                            />
                                        </Col>
                                        <Col md={12}>
                                            <FormatTextDetail
                                                label='NHÂN VIÊN KINH DOANH'
                                                p={data?.extraInfo?.salePerson || '-'}
                                            />
                                        </Col>
                                    </Col>

                                    <Col md='4'>
                                        <Col md={12}>
                                            <FormatTextDetail
                                                label='SỐ ĐIỆN THOẠI'
                                                p={data?.phoneNumber || '-'}
                                            />
                                        </Col>
                                        <Col md={12}>
                                            <FormatTextDetail
                                                label='VAI TRÒ'
                                                p={data?.roleName || '-'}
                                            />
                                        </Col>
                                    </Col>
                                </Row>
                            </CardBody>
                        </Card>
                    </Col>
                    <Evaluate />
                </Col>

                <FileInfo />
            </Row>

            <ModalDelete
                onDelete={handleConfirmDelete}
                onClose={handleClose}
                isOpen={isDeleteModalOpen}
                page='cá nhân'
                data={[data?.name]}
            />
        </div>
    );
};

export default DetailPersonal;
